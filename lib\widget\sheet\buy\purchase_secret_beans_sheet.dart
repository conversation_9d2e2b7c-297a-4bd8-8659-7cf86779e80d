import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_starry_sky_box/pages/main/my/user_store.dart';
import 'package:flutter_starry_sky_box/res/rc.dart';
import 'package:flutter_starry_sky_box/res/styles.dart';
import 'package:flutter_starry_sky_box/widget/form/formatter/my_number_text_input_formatter.dart';
import 'package:flutter_starry_sky_box/widget/screenutil/custom_screenutil.dart';
import 'package:flutter_starry_sky_box/widget/sheet/buy/purchase_secret_beans_controller.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';

class PurchaseSecretBeansSheet {
  static show({required String tradeId, int maxNum = 0, String amount = '0'}) {
    TextEditingController numController = TextEditingController();

    showModalBottomSheet(
        context: Get.context!,
        backgroundColor: Colors.transparent,
        isDismissible: true,
        isScrollControlled: true,
        builder: (context) {
          return Material(
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(19.r),
                  topRight: Radius.circular(19.r)),
              child: GetBuilder<PurchaseSecretBeansController>(
                  init: PurchaseSecretBeansController(
                      tradeId: tradeId, maxNum: maxNum, amount: amount),
                  builder: (purchaseSecretBeansController) {
                    numController.text =
                        purchaseSecretBeansController.gmNum.toString();
                    return Stack(
                      alignment: Alignment.center,
                      clipBehavior: Clip.none,
                      children: [
                        Container(
                            width: double.infinity,
                            decoration: BoxDecoration(
                                image: const DecorationImage(
                                    image: AssetImage(
                                        'assets/images/png/md_sheet_bg.png'),
                                    fit: BoxFit.fitHeight),
                                borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(19.r),
                                    topRight: Radius.circular(19.r))),
                            child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  SizedBox(
                                    height: 16.w,
                                  ),

                                  Container(
                                    padding:
                                        EdgeInsets.only(top: 19.w, right: 20.w),
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.end,
                                      children: [
                                        GestureDetector(
                                          behavior: HitTestBehavior.translucent,
                                          onTap: () {
                                            Get.back();
                                          },
                                          child: SizedBox(
                                            width: 24.w,
                                            height: 24.w,
                                            child: SvgPicture.asset(
                                                'assets/images/svg/my/sheet_close.svg'),
                                          ),
                                        )
                                      ],
                                    ),
                                  ),

                                  SizedBox(
                                    height: 12.w,
                                  ),

                                  Container(
                                    alignment: Alignment.center,
                                    child: Text(
                                      'buy_secret_bean'.tr,
                                      style: Styles.font_custom(
                                          fontSize: 22.sp,
                                          fontWeight: FontWeight.w600,
                                          color: RC.black),
                                    ),
                                  ),

                                  SizedBox(
                                    height: 4.w,
                                  ),

                                  // Container(
                                  //   alignment: Alignment.center,
                                  //   child: Row(
                                  //     mainAxisAlignment: MainAxisAlignment.center,
                                  //     children: [
                                  //       Text('${'quantity_i_can_buy'.tr}:', style: Styles.font_custom(fontSize: 16.sp, fontWeight: FontWeight.w400, color: RC.color2D2F3B),),
                                  //       Text('${purchaseSecretBeansController.maxNum ?? 0}', style: Styles.font_custom(fontSize: 20.sp, fontWeight: FontWeight.w600, color: RC.black),),
                                  //     ],
                                  //   )
                                  // ),

                                  Container(
                                      alignment: Alignment.center,
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Text(
                                            '${'currently_selling'.tr}:',
                                            style: Styles.font_custom(
                                                fontSize: 16.sp,
                                                fontWeight: FontWeight.w400,
                                                color: RC.color2D2F3B),
                                          ),
                                          Text(
                                            '${purchaseSecretBeansController.maxNum ?? 0}',
                                            style: Styles.font_custom(
                                                fontSize: 20.sp,
                                                fontWeight: FontWeight.w600,
                                                color: RC.black),
                                          ),
                                        ],
                                      )),

                                  SizedBox(
                                    height: 24.w,
                                  ),

                                  Container(
                                    decoration: BoxDecoration(
                                        color: RC.colorF5F5F7,
                                        borderRadius:
                                            BorderRadius.circular(17.r)),
                                    margin:
                                        EdgeInsets.symmetric(horizontal: 16.w),
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 4.w, vertical: 5.w),
                                    child: Row(
                                      children: [
                                        GestureDetector(
                                          behavior: HitTestBehavior.translucent,
                                          onTap: () {
                                            purchaseSecretBeansController
                                                .jian();
                                          },
                                          child: Container(
                                            width: 57.w,
                                            height: 57.w,
                                            decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(10.r),
                                                color: RC.white),
                                            alignment: Alignment.center,
                                            child: Text(
                                              '-',
                                              style: Styles.font_custom(
                                                  fontSize: 26.sp,
                                                  fontWeight: FontWeight.w600,
                                                  color: RC.colorD9D9D9),
                                            ),
                                          ),
                                        ),
                                        Expanded(
                                          child: Container(
                                            alignment: Alignment.center,
                                            // child: Text(
                                            //   '${purchaseSecretBeansController.gmNum}',
                                            //   style: Styles.font_custom(
                                            //       fontSize: 20.sp,
                                            //       fontWeight: FontWeight.w600,
                                            //       color: RC.black),
                                            // ),
                                            child: TextField(
                                              controller: numController,
                                              inputFormatters: [
                                                FilteringTextInputFormatter
                                                    .allow(RegExp(r'[0-9]'))
                                              ], // 只允许输入数字
                                              onChanged: (value) {
                                                // 如果输入的值大于最大值，则将值设置为最大值
                                                if (int.parse(value) >
                                                    (purchaseSecretBeansController
                                                            .maxNum ??
                                                        0)) {
                                                  numController.text =
                                                      '${purchaseSecretBeansController.maxNum ?? 0}';
                                                }

                                                purchaseSecretBeansController
                                                    .input(numController.text
                                                        .trim());
                                              },
                                              keyboardType:
                                                  TextInputType.number,
                                              decoration: const InputDecoration(
                                                border:
                                                    InputBorder.none, // 移除下划线
                                              ),
                                              textAlign: TextAlign.center,
                                              style: Styles.font_custom(
                                                fontSize: 20.sp,
                                                fontWeight: FontWeight.w600,
                                                color: RC.black,
                                              ),
                                            ),
                                          ),
                                        ),
                                        GestureDetector(
                                          behavior: HitTestBehavior.translucent,
                                          onTap: () {
                                            purchaseSecretBeansController.add();
                                          },
                                          child: Container(
                                            width: 57.w,
                                            height: 57.w,
                                            decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(10.r),
                                                color: RC.white),
                                            alignment: Alignment.center,
                                            child: Text(
                                              '+',
                                              style: Styles.font_custom(
                                                  fontSize: 26.sp,
                                                  fontWeight: FontWeight.w600,
                                                  color: RC.colorD9D9D9),
                                            ),
                                          ),
                                        ),
                                        SizedBox(
                                          width: 4.w,
                                        ),
                                        GestureDetector(
                                          behavior: HitTestBehavior.translucent,
                                          onTap: () {
                                            purchaseSecretBeansController.max();
                                          },
                                          child: Container(
                                            width: 57.w,
                                            height: 57.w,
                                            decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(10.r),
                                                color: RC.white),
                                            alignment: Alignment.center,
                                            child: Text(
                                              'max'.tr,
                                              style: Styles.font_custom(
                                                  fontSize: 16.sp,
                                                  fontWeight: FontWeight.w600,
                                                  color: RC.colorD9D9D9),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),

                                  SizedBox(
                                    height: 20.w,
                                  ),

                                  SizedBox(
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Container(
                                          padding: EdgeInsets.symmetric(
                                              vertical: 8.w, horizontal: 14.w),
                                          decoration: BoxDecoration(
                                              color: RC.colorC0DDFF,
                                              borderRadius:
                                                  BorderRadius.circular(80.r)),
                                          child: Row(
                                            children: [
                                              Text(
                                                'payable'.tr,
                                                style: Styles.font_custom(
                                                    fontSize: 16.sp,
                                                    fontWeight: FontWeight.w600,
                                                    color: RC.black,
                                                    height: 1.5),
                                              ),
                                              SizedBox(
                                                width: 6.w,
                                              ),
                                              SizedBox(
                                                width: 14.w,
                                                height: 14.w,
                                                child: Image.asset(
                                                    'assets/images/png/jbh_new.png'),
                                              ),
                                              SizedBox(
                                                width: 1.w,
                                              ),
                                              Text(
                                                '${purchaseSecretBeansController.xzf.toStringAsFixed(2)}',
                                                style: Styles.font_custom(
                                                    fontSize: 18.sp,
                                                    fontWeight: FontWeight.w600,
                                                    color: RC.black,
                                                    height: 1.5),
                                              ),
                                            ],
                                          ),
                                        )
                                      ],
                                    ),
                                  ),

                                  SizedBox(
                                    height: 16.w,
                                  ),

                                  GetBuilder<UserStore>(builder: (userStore) {
                                    return SizedBox(
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Text(
                                            'my_secret_bean1'.tr,
                                            style: Styles.font_custom(
                                                fontSize: 12.sp,
                                                fontWeight: FontWeight.w400,
                                                color: RC.black),
                                          ),
                                          SizedBox(
                                            width: 5.w,
                                          ),
                                          SizedBox(
                                            width: 14.w,
                                            height: 14.w,
                                            // child: Image.asset('assets/images/png/jbh_new.png'),
                                            child: Image.asset(
                                                'assets/images/png/djb_top.png'),
                                          ),
                                          SizedBox(
                                            width: 3.w,
                                          ),
                                          // Text('0.00000', style: Styles.font_custom(fontSize: 16.sp, fontWeight: FontWeight.w600, color: RC.black),),
                                          Text(
                                            '${userStore.userModel?.douCoin ?? 0.00}',
                                            style: Styles.font_custom(
                                                fontSize: 16.sp,
                                                fontWeight: FontWeight.w600,
                                                color: RC.black),
                                          ),
                                        ],
                                      ),
                                    );
                                  }),

                                  GetBuilder<UserStore>(builder: (userStore) {
                                    return SizedBox(
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Text(
                                            'my_t_coin'.tr,
                                            style: Styles.font_custom(
                                                fontSize: 12.sp,
                                                fontWeight: FontWeight.w400,
                                                color: RC.black),
                                          ),
                                          SizedBox(
                                            width: 5.w,
                                          ),
                                          SizedBox(
                                            width: 14.w,
                                            height: 14.w,
                                            child: Image.asset(
                                                'assets/images/png/jbh_new.png'),
                                          ),
                                          SizedBox(
                                            width: 3.w,
                                          ),
                                          // Text('0.00000', style: Styles.font_custom(fontSize: 16.sp, fontWeight: FontWeight.w600, color: RC.black),),
                                          Text(
                                            '${userStore.userModel?.coin ?? 0.00}',
                                            style: Styles.font_custom(
                                                fontSize: 16.sp,
                                                fontWeight: FontWeight.w600,
                                                color: RC.black),
                                          ),
                                        ],
                                      ),
                                    );
                                  }),

                                  SizedBox(
                                    height: 30.w,
                                  ),
                                  GestureDetector(
                                    behavior: HitTestBehavior.translucent,
                                    onTap: () {
                                      purchaseSecretBeansController.submit();
                                    },
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Container(
                                          padding: EdgeInsets.symmetric(
                                              vertical: 16.w, horizontal: 97.w),
                                          decoration: BoxDecoration(
                                              image: purchaseSecretBeansController
                                                          .xzf >
                                                      0
                                                  ? const DecorationImage(
                                                      image: AssetImage(
                                                          'assets/images/png/sheet_bg_show.png'))
                                                  : const DecorationImage(
                                                      image: AssetImage(
                                                          'assets/images/png/sheet_bg.png')),
                                              borderRadius:
                                                  BorderRadius.circular(80.r)),
                                          child: Text(
                                            'finalize_a_purchase'.tr,
                                            style: Styles.font_custom(
                                                fontSize: 18.sp,
                                                fontWeight: FontWeight.w600,
                                                color: RC.white),
                                          ),
                                        )
                                      ],
                                    ),
                                  ),

                                  SizedBox(
                                    height: 18.w,
                                  )
                                ])),

                        // Positioned(
                        //   top: -61.w,
                        //   child: SizedBox(
                        //     width: 99.w,
                        //     height: 99.w,
                        //     child: Image.asset('assets/images/png/djb_top.png'),
                        //   )
                        // )

                        Positioned(
                            top: -100.w,
                            child: SizedBox(
                              width: 180.w,
                              height: 180.w,
                              child: Lottie.asset(
                                  'assets/lottie/new_douzi.json',
                                  fit: BoxFit.contain),
                            ))
                      ],
                    );
                  }));
        });
  }
}
