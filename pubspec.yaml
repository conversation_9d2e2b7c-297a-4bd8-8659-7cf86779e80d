name: flutter_starry_sky_box
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.1.03+1

environment:
  # sdk: '>=3.2.4 <4.0.0'
  sdk: '>=3.0.3 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  # https://pub.dev/packages/get
  get: ^4.6.5
  # https://pub.dev/packages/dio
  dio: ^4.0.6
  # https://pub.dev/packages/connectivity_plus
  connectivity_plus: ^6.1.4
  # https://pub.dev/packages/pretty_dio_logger
  pretty_dio_logger: ^1.1.1
  # https://pub.dev/packages/flustars_flutter3
  flustars_flutter3: ^3.0.0
  # https://pub.dev/packages/pull_to_refresh_flutter3
  pull_to_refresh_flutter3: ^2.0.1
  # https://pub.dev/packages/flutter_easyloading
  flutter_easyloading: ^3.0.5
  # https://pub.dev/packages/flutter_inappwebview
  flutter_inappwebview: ^6.1.5
  # https://pub.dev/packages/flutter_svg
  flutter_svg: 2.0.10+1
  # https://pub.dev/packages/carousel_slider
  carousel_slider: ^5.0.0
  # https://pub.dev/packages/extended_nested_scroll_view
  extended_nested_scroll_view: ^6.2.1
  # https://pub.dev/packages/fl_chart
  fl_chart: 0.69.2


  video_player: ^2.7.0
  preload_page_view: ^0.2.0
  # https://pub.dev/packages/chewie
  chewie: ^1.7.5
  # https://pub.dev/packages/visibility_detector
  visibility_detector: ^0.4.0+2
  # https://pub.dev/packages/bruno
  bruno: ^3.4.3
  # https://pub.dev/packages/event_bus_plus
  event_bus_plus: ^0.6.1
  # https://pub.dev/packages/crypto
  crypto: 3.0.3
  # https://pub.dev/packages/device_info_plus
  device_info_plus: ^10.1.2
  # https://pub.dev/packages/cached_network_image
  cached_network_image: 3.3.0
  # https://pub.dev/packages/wechat_camera_picker
  wechat_camera_picker: 4.3.2
  # https://pub.dev/packages/pretty_qr_code
  pretty_qr_code: ^3.3.0
  # https://pub.dev/packages/path_provider
  path_provider: ^2.1.5
  # https://pub.dev/packages/image_gallery_saver
  # image_gallery_saver: ^2.0.3
  # https://pub.dev/packages/image_gallery_saver_plus
  image_gallery_saver_plus: ^3.0.5
  # https://pub.dev/packages/tencent_cos_plus
  tencent_cos_plus: ^1.2.2
  # https://pub.dev/packages/flutter_widget_from_html
  flutter_widget_from_html: ^0.15.3
  # https://pub.dev/packages/video_compress
  video_compress: ^3.1.4


  package_info_plus: ^8.3.0
  permission_handler: ^11.3.1
  # google_sign_in: 6.1.6
  # firebase_core: ^2.24.2
  # flutter_telegram_login: ^0.0.2

  flutter_hls_parser: ^2.0.0  # 解析m3u8文件
  # ffmpeg_kit_flutter: 6.0.3  # 合成mp4文件
  # ffmpeg_kit_flutter_new: ^1.4.1

  # https://pub.dev/packages/wechat_assets_picker
  wechat_assets_picker: ^9.3.3
  barcode_scan2: ^4.5.0
  # 侧滑删除  https://pub.dev/packages/flutter_slidable/versions
  flutter_slidable: 3.1.2
#  google_api_availability: ^4.0.0
  google_api_availability: ^5.0.1

  lottie: 3.1.1

  tencent_cloud_chat_uikit:
    path: ./plugins/chat-uikit-flutter-main
  tencent_calls_uikit: ^3.1.2
  tencent_cloud_chat_push: ^8.5.6864
  tencent_chat_i18n_tool: ^2.3.8
  tencent_cloud_chat_sdk: ^8.5.6864+6

  bitsdojo_window: ^0.1.5
  provider: ^6.1.5
  flutter_picker:
    path: ./plugins/flutter_picker-master
  # preload_video: ^0.0.7

  # openinstall_flutter_plugin: ^2.5.4
  openinstall_flutter_global: ^1.0.0
  video_player_web_hls: 1.1.1
  scrollview_observer: ^1.26.0
  google_sign_in: 6.1.6
#  firebase_core: ^2.24.2
  firebase_core: ^3.13.1
  firebase_analytics: ^11.5.1
#  firebase_analytics: 10.8.9
  # firebase_messaging: 14.7.19
  # firebase_app_installations: 0.2.4+17
#  flutter_fcm: 1.2.1
  flutter_fcm: ^1.3.2
  flutter_contacts: ^1.1.9+2
  url_launcher: ^6.3.2
  mobile_scanner: ^6.0.10
  wakelock_plus: ^1.3.2



dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^2.0.0

# 依赖覆盖，解决版本冲突
dependency_overrides:
  tencent_chat_i18n_tool:
#    path: ../tencent_chat_i18n_tool
    git:
      url: https://gitee.com/YoYoHenryGao/tencent_chat_i18n_tool.git
      ref: 2.3.18

#  intl: ^0.18.0  # 强制使用与 bruno 兼容的 intl 版本
  url_launcher_ios: 6.3.1
  http: ^0.13.6
  protobuf: ^2.1.0  # 强制使用 protobuf 2.x 版本以解决依赖冲突
  path_drawing: ^1.0.0  # 强制使用与 bruno 兼容的 path_drawing 版本

  path: 1.8.3
  permission_handler: ^11.3.1
  intl: ^0.19.0
  #  file_picker: ^8.3.0
  file_picker: ^10.1.9
  open_file: ^3.5.10
  better_player_plus: ^1.0.8
  flutter_widget_from_html: ^0.15.0
  flutter_local_notifications: ^19.2.1


  flutter_facebook_auth: ^7.0.1 # facebook登录
  # whatsapp:
  whatsapp:
  # tiktok_login_flutter: ^1.0.0
  # tiktok_api: ^0.0.13
  shimmer: ^3.0.0 # 加载动画
  share_plus: ^7.2.2
  just_audio: ^0.9.42
  adjust_sdk: ^5.4.1
  svgaplayer_flutter: ^2.0.0
  # 使用与Flutter 3.24.0兼容的win32版本
#  win32: 5.10.1
#  package_info_plus: 4.2.0
#  # 排除有问题的Windows包
#  flutter_secure_storage_windows: 3.1.2

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  assets:
    - assets/images/png/
    - assets/images/png/level/
    - assets/images/png/shared/
    - assets/images/png/video/
    - assets/images/png/upgrade/
    - assets/images/png/checkin/
    - assets/images/png/bank_bind/
    - assets/images/png/vip/
    - assets/images/png/send/
    - assets/images/png/my/
    - assets/images/png/chat/
    - assets/images/svg/
    - assets/images/svg/guide/
    - assets/images/svg/home/
    - assets/images/svg/my/
    - assets/images/svg/message/
    - assets/images/svg/task/
    - assets/images/svg/login/
    - assets/images/svg/bank_list/
    - assets/images/svg/send/
    - assets/images/svg/chat/
    - assets/video/
    - assets/images/png/jy/
    - assets/lottie/
    - assets/lottie/sign/
    - assets/images/png/share/
    - assets/custom_face_resource/4350/
    - assets/custom_face_resource/4351/
    - assets/custom_face_resource/4352/
    - assets/custom_face_resource/4353/
    - assets/custom_face_resource/4354/
    - assets/custom_face_resource/4355/
    - assets/sound/
    - assets/svga/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: CarterOne
      fonts:
        - asset: assets/fonts/CarterOne-Regular.ttf
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
