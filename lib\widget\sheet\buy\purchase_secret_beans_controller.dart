import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_starry_sky_box/api/api_response.dart';
import 'package:flutter_starry_sky_box/api/req/trade/req_trade.dart';
import 'package:flutter_starry_sky_box/pages/main/my/user_store.dart';
import 'package:flutter_starry_sky_box/utils/custom_constant.dart';
import 'package:flutter_starry_sky_box/utils/custom_event_util.dart';
import 'package:flutter_starry_sky_box/widget/toast/custom_toast.dart';
import 'package:get/get.dart';

import '../../../utils/adjust_utils.dart';

class PurchaseSecretBeansController extends GetxController {
  PurchaseSecretBeansController({this.tradeId, this.maxNum, this.amount});

  String? tradeId;
  int? maxNum;
  String? amount;

  int gmNum = 0;
  double xzf = 0;

  add() {
    if (gmNum >= (maxNum ?? 0)) {
      return;
    }
    gmNum++;

    if (gmNum >= (maxNum ?? 0)) {
      gmNum = (maxNum ?? 0);
    }

    xzf = (gmNum) * (double.tryParse('${amount}') ?? 0);
    update();
  }

  input(String num) {
    gmNum = int.parse(num);
    if (gmNum >= (maxNum ?? 0)) {
      gmNum = (maxNum ?? 0);
    }

    xzf = (gmNum) * (double.tryParse('${amount}') ?? 0);
    update();
  }

  jian() {
    if (gmNum <= 0) {
      return;
    }
    gmNum--;

    xzf = (gmNum) * (double.tryParse('${amount}') ?? 0);
    update();
  }

  max() {
    gmNum = (maxNum ?? 0);

    xzf = (gmNum) * (double.tryParse('${amount}') ?? 0);
    update();
  }

  submit() async {
    if (gmNum <= 0) {
      CustomToast.showTextToast('please_add_purchase_quantity'.tr);
      return;
    }
    try {
      EasyLoading.show(status: '${'under_purchase'.tr}...');

      var resp = await ReqTrade.setTrade(num: gmNum, tradeId: tradeId ?? '');

      if (resp.status == Status.completed) {
        if (resp.data['data']['code'] == 0) {
          UserStore.to.getBaseInfo();
          // 发送Adjust事件
          AdJustUtils.trackEvent(
              'T-Coin 兑换 F-Bean成功，T-Coin数量：$gmNum，F-Bean数量：$xzf');

          eventBus
              .fire(const BottomNavigationBarEvent(CustomConstant.BUY_MIDOU));
          CustomToast.showTextToast('purchase_successful'.tr);
          Get.back();
        } else {
          CustomToast.showTextToast('${resp.data['data']['msg']}');
        }
      } else {
        CustomToast.showTextToast(
            '${resp.data['data']['msg'] ?? 'the_system_is_busy_please_try_again_later'.tr}');
      }
    } catch (e) {
      CustomToast.showTextToast('the_system_is_busy_please_try_again_later'.tr);
    }
  }
}
