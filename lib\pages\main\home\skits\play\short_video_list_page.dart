import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_starry_sky_box/pages/main/home/<USER>/play/short_video_item.dart';
import 'package:flutter_starry_sky_box/pages/main/home/<USER>/play/short_video_list_controller.dart';
import 'package:flutter_starry_sky_box/res/rc.dart';
import 'package:flutter_starry_sky_box/res/styles.dart';
import 'package:flutter_starry_sky_box/utils/custom_logger.dart';
import 'package:flutter_starry_sky_box/widget/default_page/custom_loading.dart';
import 'package:flutter_starry_sky_box/widget/screenutil/custom_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:flutter_starry_sky_box/widget/custom_preload_page_view.dart';

import '../../../../../routers/app_routes.dart';
import '../../../../../utils/custom_sp_util.dart';

/// 短视频播放列表
class ShortVideoListPage extends StatelessWidget {
  const ShortVideoListPage({super.key, this.skitsid = '0', this.type = '0'});

  final String skitsid;

  // 0-短剧主页 1-热门 2-搜索
  final String type;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: RC.black,
      resizeToAvoidBottomInset: false,
      body: GetBuilder<ShortVideoListController>(
          init: ShortVideoListController(skitsid: skitsid, type: type),
          builder: (shortVideoListController) {
            return Stack(
              children: [
                shortVideoListController.isLoading == true
                    ? CustomLoading(color: RC.white)
                    : shortVideoListController.getSkitsModel == null
                        ? const SizedBox()
                        : Container(
                            width: double.infinity,
                            height: double.infinity,
                            color: Colors.black,
                            child: (shortVideoListController.getSkitsModel
                                            ?.episodeslist?.length ??
                                        0) ==
                                    0
                                ? const SizedBox()
                                : PreloadPageView.builder(
                                    preloadPagesCount: 4,
                                    // physics: const AlwaysScrollableScrollPhysics(),
                                    scrollDirection: Axis.vertical,
                                    itemCount: shortVideoListController
                                            .getSkitsModel
                                            ?.episodeslist
                                            ?.length ??
                                        0,
                                    itemBuilder:
                                        (BuildContext context, int position) {
                                      return ShortVideoItem(
                                        episodeslist: shortVideoListController
                                            .getSkitsModel!
                                            .episodeslist![position],
                                        getSkits: shortVideoListController
                                            .getSkitsModel,
                                        index: position,
                                        shortVideoListController:
                                            shortVideoListController,
                                        type: type,
                                        playSuccess: (next) async {
                                          if (position + 1 >
                                              shortVideoListController
                                                      .getSkitsModel!
                                                      .episodeslist!
                                                      .length -
                                                  1) {
                                            return;
                                          }
                                          // shortVideoListController.preloadPageController?.jumpToPage(next + 1);
                                          shortVideoListController
                                              .preloadPageController
                                              ?.jumpToPage(position + 1);
                                        },
                                        position:
                                            shortVideoListController.position,
                                      );
                                    },
                                    controller: shortVideoListController
                                        .preloadPageController!,
                                    onPageChanged: (int position) {
                                      var isLogin = CustomSpUtil.getUid() != 0;
                                      var index = shortVideoListController
                                          .getSkitsModel?.nowEpisodes;
                                      if (isLogin || (!isLogin && index! < 7)) {
                                        shortVideoListController
                                            .skitsAddView(position + 1);
                                      } else {
                                        Get.toNamed(AppRoutes.login);
                                      }
                                    },
                                    onLoadMore: () {
                                      logger.d('=====message');
                                    },
                                  ),
                          ),
                Positioned(
                    child: SafeArea(
                  child: Row(
                    children: [
                      IconButton(
                          onPressed: () {
                            Get.back();
                          },
                          icon: SvgPicture.asset(
                            'assets/images/svg/message/icon_back_white.svg',
                            width: 18.w,
                          )),
                      // icon: Image.asset('assets/images/svg/message/icon_right_array.png', color: RC.white, width: 18)),
                      if ((shortVideoListController
                                  .getSkitsModel?.episodeslist?.length ??
                              0) >
                          0) ...[
                        Text(
                          'episode_param'.trParams({
                            'param':
                                '${shortVideoListController.getSkitsModel?.nowEpisodes == -1 ? 1 : shortVideoListController.getSkitsModel?.nowEpisodes == 0 ? 1 : shortVideoListController.getSkitsModel?.nowEpisodes}'
                          }),
                          style: Styles.font_custom(
                              fontSize: 15.sp,
                              fontWeight: FontWeight.w400,
                              color: RC.white),
                        )
                      ]
                    ],
                  ),
                ))
              ],
            );
          }),
    );
  }
}
